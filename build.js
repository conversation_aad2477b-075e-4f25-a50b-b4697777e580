﻿// Node entry (Windows-friendly, no hashbang)

/**
 * GamePort 闈欐€佺綉绔欐瀯寤鸿剼鏈?
 * 鐢熸垚涓婚〉鍜屾父鎴忛〉闈紝浣跨敤鐩稿悓鐨勬ā鏉跨粨鏋?
 */

const fs = require('fs');
const path = require('path');

// 閰嶇疆
const CONFIG = {
    srcDir: './src',
    distDir: './dist',
    templatesDir: './src/templates',
    componentsDir: './src/components',
    gamesDataFile: './games.json'
};

// 纭繚杈撳嚭鐩綍瀛樺湪
function ensureDirectoryExists(dirPath) {
    if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
        console.log(`馃搧 Created directory: ${dirPath}`);
    }
}

// 璇诲彇鏂囦欢鍐呭
function readFile(filePath) {
    try {
        return fs.readFileSync(filePath, 'utf8');
    } catch (error) {
        console.error(`鉂?Error reading file ${filePath}:`, error.message);
        return '';
    }
}

// 鍐欏叆鏂囦欢
function writeFile(filePath, content) {
    try {
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`鉁?Generated: ${filePath}`);
    } catch (error) {
        console.error(`鉂?Error writing file ${filePath}:`, error.message);
    }
}

// 澶嶅埗鐩綍
function copyDirectory(src, dest) {
    ensureDirectoryExists(dest);
    const items = fs.readdirSync(src);

    items.forEach(item => {
        const srcPath = path.join(src, item);
        const destPath = path.join(dest, item);

        if (fs.statSync(srcPath).isDirectory()) {
            copyDirectory(srcPath, destPath);
        } else {
            fs.copyFileSync(srcPath, destPath);
        }
    });
}

// 璇诲彇娓告垙鏁版嵁
function loadGamesData() {
    try {
        const data = fs.readFileSync(CONFIG.gamesDataFile, 'utf8');
        const gamesArray = JSON.parse(data);

        // 鏍规嵁PRD 2.4鑺傝姹傦細
        // 鏁扮粍绗竴涓璞0]涓撻棬鐢ㄤ簬鐢熸垚index.html鐨勬牳蹇冨唴瀹?
        // 鏁扮粍浠庣浜屼釜瀵硅薄[1]寮€濮嬩负鍒楄〃娓告垙
        return {
            homepageGame: gamesArray[0] || null,
            listGames: gamesArray.slice(1) || []
        };
    } catch (error) {
        console.error(`鉂?Error loading games data:`, error.message);
        return {
            homepageGame: null,
            listGames: []
        };
    }
}

// 璇诲彇閰嶇疆鏁版嵁
function loadConfig() {
    try {
        const data = fs.readFileSync('./config.json', 'utf8');
        return JSON.parse(data);
    } catch (error) {
        console.error(`鉂?Error loading config:`, error.message);
        return {
            site_name: 'GamePort',
            selected_theme: 'default',
            site_url: 'https://gameport.example.com'
        };
    }
}

// ===== I18N Utilities =====
function readJsonSafe(filePath, fallback = {}) {
    try {
        if (!fs.existsSync(filePath)) return fallback;
        const raw = fs.readFileSync(filePath, 'utf8');
        return JSON.parse(raw);
    } catch (e) {
        console.warn(`鈿狅笍 Failed to read JSON ${filePath}:`, e.message);
        return fallback;
    }
}

function loadLanguagePack(lang) {
    const file = path.join('./i18n', `${lang}.json`);
    return readJsonSafe(file, {});
}

function getNested(obj, key, d = undefined) {
    if (!obj || !key) return d;
    return key.split('.').reduce((acc, k) => (acc && Object.prototype.hasOwnProperty.call(acc, k) ? acc[k] : d), obj);
}

function createTranslator(primaryPack, fallbackPack = {}) {
    return function t(key, fallback = '') {
        const v = getNested(primaryPack, key);
        if (v !== undefined && v !== null) return String(v);
        const fb = getNested(fallbackPack, key);
        if (fb !== undefined && fb !== null) return String(fb);
        return String(fallback || '');
    };
}

let I18N_CTX = { lang: 'en', t: (k, fb = '') => String(fb || '') };

function setI18nContext(lang, fallbackLang = 'en') {
    const primary = loadLanguagePack(lang);
    const fallback = fallbackLang && fallbackLang !== lang ? loadLanguagePack(fallbackLang) : {};
    I18N_CTX = { lang, t: createTranslator(primary, fallback) };
    console.log(`馃寪 I18N context set: ${lang}`);
}

function i18nApply(html) {
    const t = I18N_CTX.t;
    const lang = I18N_CTX.lang;
    return String(html || '')
        .replace(/\{\{LANG\}\}/g, lang)
        .replace(/\{\{T_SEARCH_PLACEHOLDER\}\}/g, t('search.placeholder', 'Search games...'))
        .replace(/\{\{T_PLAY_NOW\}\}/g, t('btn.play_now', 'Play Now'))
        .replace(/\{\{T_VIEW_ALL\}\}/g, t('btn.view_all', 'View All'))
        .replace(/\{\{T_FULLSCREEN\}\}/g, t('btn.fullscreen', 'Fullscreen'))
        .replace(/\{\{T_FOOTER_RIGHTS\}\}/g, t('footer.rights', 'All rights reserved.'))
        .replace(/\{\{T_FOOTER_TAGLINE\}\}/g, t('footer.tagline', 'Discover and play the best online games for free!'))
        .replace(/\{\{T_404_TITLE\}\}/g, t('404.title', 'Page Not Found!'))
        .replace(/\{\{T_404_DESC\}\}/g, t('404.desc', "Sorry, the page you are looking for doesn't exist."))
        .replace(/\{\{T_404_CTA_HOME\}\}/g, t('404.cta_home', 'Go Home'))
        .replace(/\{\{LABEL_POPULAR\}\}/g, t('nav.popular', 'Popular Games'))
        .replace(/\{\{LABEL_NEW\}\}/g, t('nav.new', 'New Games'))
        .replace(/\{\{LANG_SWITCH_LABEL\}\}/g, t('lang.switch_label', '涓枃'))

;
}

function localizeValue(val, lang) {
    if (val && typeof val === 'object') {
        if (val[lang] != null) return val[lang];
        if (val['en'] != null) return val['en'];
        return '';
    }
    return val != null ? val : '';
}

function localizeFeatureBlocks(blocks, lang) {
    if (!Array.isArray(blocks)) return [];
    return blocks.map(b => {
        const nb = { ...b };
        if ('title' in nb) nb.title = localizeValue(nb.title, lang);
        if ('content' in nb) nb.content = localizeValue(nb.content, lang);
        if ('text' in nb) nb.text = localizeValue(nb.text, lang);
        return nb;
    });
}

function localizeGame(game, lang) {
    if (!game) return game;
    const g = { ...game };
    if (g.meta) {
        g.meta = { ...g.meta };
        if ('title' in g.meta) g.meta.title = localizeValue(g.meta.title, lang);
        if ('description' in g.meta) g.meta.description = localizeValue(g.meta.description, lang);
    }
    if (Array.isArray(g.feature_blocks)) {
        g.feature_blocks = localizeFeatureBlocks(g.feature_blocks, lang);
    }
    return g; // name unchanged
}

// 鑾峰彇娓告垙鐨勬湁鏁坕frame閰嶇疆
function getGameIframeConfig(game, globalConfig) {
    // 妫€鏌ユ父鎴忔槸鍚︽湁iframe_config涓攅nable涓簍rue
    if (game && game.iframe_config && game.iframe_config.enable === true) {
        // 楠岃瘉閰嶇疆鏈夋晥鎬?
        const width = game.iframe_config.width;
        const height = game.iframe_config.height;

        // 鍩烘湰楠岃瘉锛氱‘淇濇槸鏈夋晥鏁板瓧涓斿湪鍚堢悊鑼冨洿鍐?
        if (typeof width === 'number' && typeof height === 'number' &&
            width >= 400 && width <= 2560 && height >= 300 && height <= 1440) {
            console.log(`馃幃 Using game-specific iframe config for ${game.id}: ${width}脳${height}`);
            return {
                width: width,
                height: height
            };
        } else {
            console.warn(`鈿狅笍 Invalid iframe config for ${game.id}, falling back to global config`);
        }
    }

    // enable涓篺alse鎴栨湭閰嶇疆鏃讹紝浣跨敤鍏ㄥ眬閰嶇疆
    if (game && game.iframe_config && game.iframe_config.enable === false) {
        console.log(`馃搵 Using global iframe config for ${game.id} (enable=false)`);
    }

    // 浣跨敤鍏ㄥ眬閰嶇疆
    return {
        width: globalConfig?.width || 854,
        height: globalConfig?.height || 480
    };
}

// 鏍规嵁鏈夋晥鐨?iframe 瀹藉害閫夋嫨甯冨眬绫伙紝纭繚涓诲垪瀹藉害涓嶅皬浜巌frame瀹藉害
function getIframeSizeClassByWidth(width) {
    if (typeof width !== 'number') return 'iframe-large';
    if (width <= 854) return 'iframe-small';
    if (width <= 960) return 'iframe-large';
    return 'iframe-xlarge';
}


// 鐢熸垚娓告垙鍗＄墖HTML
function generateGameCard(game) {
    const cardTemplate = readFile(path.join(CONFIG.componentsDir, 'game-card.html'));

    return cardTemplate
        .replace(/\{\{GAME_ID\}\}/g, game.id)
        .replace(/\{\{GAME_NAME\}\}/g, game.name)
        .replace(/\{\{GAME_THUMBNAIL\}\}/g, game.thumbnail)
        .replace(/\{\{GAME_CATEGORY\}\}/g, game.category);
}

// 鐢熸垚娓告垙鍒楄〃HTML
function generateGamesList(games, category = null) {
    let filteredGames = games;

    if (category) {
        filteredGames = games.filter(game => game.category === category);
    }

    return filteredGames.map(game => generateGameCard(game)).join('\n');
}

// 鏍规嵁閰嶇疆閫夋嫨娓告垙
function selectGamesForDisplay(allGames, displayConfig, fallbackCategory = null) {
    if (!displayConfig) {
        console.warn('鈿狅笍 No display config provided, using fallback');
        return fallbackCategory ?
            allGames.filter(game => game.category === fallbackCategory).slice(0, 6) :
            allGames.slice(0, 6);
    }

    const { count = 6, selection_method = 'sequential', selected_games = [] } = displayConfig;
    let selectedGames = [];

    switch (selection_method) {
        case 'configured':
            // 閰嶇疆鍖栭€夋嫨锛氭牴鎹畇elected_games鏁扮粍涓殑ID鏌ユ壘娓告垙
            selectedGames = selected_games
                .map(gameId => allGames.find(game => game.id === gameId))
                .filter(game => game !== undefined) // 杩囨护鎺変笉瀛樺湪鐨勬父鎴?
                .slice(0, count);

            // 濡傛灉閰嶇疆鐨勬父鎴忔暟閲忎笉瓒筹紝鐢ㄩ『搴忛€夋嫨琛ュ厖
            if (selectedGames.length < count && fallbackCategory) {
                const remainingCount = count - selectedGames.length;
                const fallbackGames = allGames
                    .filter(game => game.category === fallbackCategory)
                    .filter(game => !selectedGames.some(selected => selected.id === game.id))
                    .slice(0, remainingCount);
                selectedGames = [...selectedGames, ...fallbackGames];
            }

            console.log(`馃幆 Configured selection: ${selectedGames.length}/${count} games selected`);
            break;

        case 'random':
            // 闅忔満閫夋嫨
            const availableGames = fallbackCategory ?
                allGames.filter(game => game.category === fallbackCategory) :
                allGames;
            const shuffled = [...availableGames].sort(() => Math.random() - 0.5);
            selectedGames = shuffled.slice(0, count);
            console.log(`馃幉 Random selection: ${selectedGames.length} games selected`);
            break;

        case 'sequential':
        default:
            // 椤哄簭閫夋嫨锛堥粯璁わ級
            const sequentialGames = fallbackCategory ?
                allGames.filter(game => game.category === fallbackCategory) :
                allGames;
            selectedGames = sequentialGames.slice(0, count);
            console.log(`馃搵 Sequential selection: ${selectedGames.length} games selected`);
            break;
    }

    return selectedGames;
}

// 棰勪及Feature Blocks鐨勯珮搴?
function estimateFeatureHeight(featureBlocks) {
    if (!featureBlocks || !Array.isArray(featureBlocks)) {
        return 0;
    }

    let totalHeight = 0;

    featureBlocks.forEach(block => {
        switch (block.type) {
            case 'youtube':
                // YouTube瑙嗛鍧楋細鏍囬(60px) + 瑙嗛(400px) + 闂磋窛(40px)
                totalHeight += 500;
                break;

            case 'section':
                // 鏂囨湰娈佃惤鍧楋細鏍囬(60px) + 鍐呭 + 闂磋窛(40px)
                const contentLength = block.content ? block.content.length : 0;
                const format = block.format || 'paragraph';

                if (format === 'list') {
                    // 鍒楄〃鏍煎紡锛氭瘡涓垪琛ㄩ」绾?0px
                    const listItems = block.content.split('|').filter(item => item.trim());
                    totalHeight += 60 + (listItems.length * 30) + 40;
                } else {
                    // 娈佃惤鏍煎紡锛氱害50瀛楃/琛岋紝20px/琛?
                    const estimatedLines = Math.ceil(contentLength / 50);
                    totalHeight += 60 + (estimatedLines * 20) + 40;
                }
                break;

            case 'heading':
                // 鏍囬鍧楋細60px + 闂磋窛(20px)
                totalHeight += 80;
                break;

            case 'paragraph':
                // 绾钀藉潡锛氭牴鎹唴瀹归暱搴︿及绠?
                const paragraphLength = block.content ? block.content.length : 0;
                const paragraphLines = Math.ceil(paragraphLength / 50);
                totalHeight += (paragraphLines * 20) + 30;
                break;

            case 'custom_html':
                // 鑷畾涔塇TML鍧楋細淇濆畧浼扮畻300px
                totalHeight += 300;
                break;

            default:
                // 鏈煡绫诲瀷锛氫繚瀹堜及绠?00px
                totalHeight += 100;
        }
    });

    return totalHeight;
}

// 鐢熸垚Feature Blocks HTML
function generateFeatureBlocks(featureBlocks) {
    if (!featureBlocks || !Array.isArray(featureBlocks)) {
        return '';
    }

    // 棰勪及鎬婚珮搴?
    const estimatedHeight = estimateFeatureHeight(featureBlocks);
    const needsCollapse = estimatedHeight > 1000;

    const featureBlocksTemplate = readFile(path.join(CONFIG.componentsDir, 'feature-blocks.html'));

    // 鐢熸垚鎵€鏈塮eature blocks鐨凥TML
    const blocksHtml = featureBlocks.map(block => {
        let blockHtml = '';

        switch (block.type) {
            case 'youtube':
                blockHtml = `
                <div class="feature-block youtube-block">
                    <h2>${block.title || 'Watch Gameplay'}</h2>
                    <div class="youtube-embed">
                        <iframe
                            src="https://www.youtube.com/embed/${block.videoId}"
                            title="${block.title || 'Game Video'}"
                            frameborder="0"
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                            allowfullscreen>
                        </iframe>
                    </div>
                </div>`;
                break;

            case 'section':
                // 妫€娴媐ormat瀛楁锛屾敮鎸乸aragraph锛堥粯璁わ級鍜宭ist涓ょ鏍煎紡
                const format = block.format || 'paragraph';
                let contentHtml = '';

                if (format === 'list') {
                    // list鏍煎紡锛氭寜|鍒嗛殧绗︽媶鍒嗘垚鍒楄〃椤?
                    const listItems = block.content.split('|').map(item => item.trim()).filter(item => item);
                    contentHtml = `<ol>${listItems.map(item => `<li>${item}</li>`).join('')}</ol>`;
                } else {
                    // paragraph鏍煎紡锛堥粯璁わ級锛氬皢|鏇挎崲涓哄彞鍙?绌烘牸
                    const paragraphContent = block.content.replace(/\|/g, '. ');
                    contentHtml = `<p>${paragraphContent}</p>`;
                }

                blockHtml = `
                <div class="feature-block section-block">
                    <h2>${block.title}</h2>
                    ${contentHtml}
                </div>`;
                break;

            case 'heading':
                blockHtml = `
                <div class="feature-block heading-block">
                    <h2>${block.text}</h2>
                </div>`;
                break;

            case 'paragraph':
                blockHtml = `
                <div class="feature-block paragraph-block">
                    <p>${block.content}</p>
                </div>`;
                break;

            case 'custom_html':
                blockHtml = `
                <div class="feature-block custom-html-block">
                    ${block.html}
                </div>`;
                break;

            default:
                console.warn(`鈿狅笍 Unknown feature block type: ${block.type}`);
        }


        return blockHtml;
    }).join('\n');

    // 濡傛灉闇€瑕佹姌鍙狅紝鍖呰鍦ㄦ姌鍙犲鍣ㄤ腑
    if (needsCollapse) {
        return `
            <div class="feature-content-wrapper">
                <div class="feature-collapsible" data-estimated-height="${estimatedHeight}">
                    ${blocksHtml}
                </div>
                <div class="feature-expand-control">
                    <button class="feature-expand-btn" data-expanded="false" aria-expanded="false">
                        <span class="expand-text">Show More</span>
                        <span class="expand-icon">鈻?/span>
                    </button>
                </div>
            </div>
        `;
    }

    // 涓嶉渶瑕佹姌鍙狅紝鐩存帴杩斿洖鍐呭
    return blocksHtml;
}

// 鐢熸垚FAQ Section HTML锛堜粠config.json璇诲彇锛?
function generateFAQSection(config, context = 'home') {
    const faq = config.faq || {};
    const enabled = faq.enabled !== false;
    const showOnHome = faq.show_on_homepage !== false;
    const showOnGame = faq.show_on_game_pages === true; // 榛樿涓嶅湪娓告垙璇︽儏灞曠ず

    const shouldShow = context === 'home' ? (enabled && showOnHome) : (enabled && showOnGame);
    if (!shouldShow || !Array.isArray(faq.items) || faq.items.length === 0) {
        return '';
    }

    const title = faq.title || 'FAQ';
    const itemsHtml = faq.items.map(item => {
        const q = item.question || '';
        const a = item.answer || '';
        return `
            <div class=\"faq-item\">\n                <h3 class=\"faq-question\">${q}</h3>\n                <p class=\"faq-answer\">${a}</p>\n            </div>`;
    }).join('');

    const sectionHtml = `
        <section class=\"faq-section\">\n            <div class=\"section-header\">\n                <h2 class=\"section-title\">${title}</h2>\n            </div>\n            <div class=\"faq-content\">${itemsHtml}\n            </div>\n        </section>`;

    // 娉ㄥ叆鍚庣殑绔欑偣鍗犱綅绗︽浛鎹紙纭繚FAQ涓殑{{SITE_NAME}}绛夎鏇挎崲锛?
    return sectionHtml
        .replace(/\{\{SITE_NAME\}\}/g, config.site_name || 'GamePort')
        .replace(/\{\{SITE_URL\}\}/g, config.site_url || 'https://gameport.example.com');
}

// 鐢熸垚娓告垙鎼滅储鏁版嵁JavaScript鏂囦欢
function generateGamesDataJS(gamesData) {
    const searchableGames = gamesData.listGames.map(game => ({
        id: game.id,
        name: game.name,
        category: game.category,
        thumbnail: game.thumbnail,
        url: game.category === 'popular'
            ? `popular_games/${game.id}.html`
            : `new_games/${game.id}.html`
    }));

    const jsContent = `// 娓告垙鎼滅储鏁版嵁 - 鏋勫缓鏃惰嚜鍔ㄧ敓鎴?
// 璇峰嬁鎵嬪姩淇敼姝ゆ枃浠?
window.GAMES_DATA = ${JSON.stringify(searchableGames, null, 2)};

// 鎼滅储鏁版嵁缁熻
console.log('馃幃 Loaded', window.GAMES_DATA.length, 'games for search');
`;

    const jsFilePath = path.join(CONFIG.distDir, 'js', 'games-data.js');
    writeFile(jsFilePath, jsContent);
    console.log(`馃搳 Generated games search data: ${searchableGames.length} games`);
}

// 鑾峰彇瀵艰埅/鍖哄潡鏍囬鐨勬爣绛?
function getDisplayLabels(config) {
    const labels = config.display_settings?.labels || {};
    return {
        popular: labels.popular || 'Popular Games',
        new: labels.new || 'New Games'
    };
}

// ===== 娓告垙鐢熸垚妫€鏌ュ嚱鏁?=====

// 妫€鏌ユ父鎴忔槸鍚﹀簲璇ョ敓鎴怘TML鏂囦欢
function checkGameShouldGenerate(game, config) {
    if (game.category === 'popular') {
        return config.display_settings?.homepage?.popular_games_display?.enabled !== false;
    }
    if (game.category === 'new') {
        return config.display_settings?.homepage?.new_games_display?.enabled !== false;
    }
    // 鍏朵粬绫诲瀷鐨勬父鎴忛粯璁ょ敓鎴愶紙濡俬omepage-special绛夛級
    return true;
}

// 妫€鏌ュ垎绫婚〉闈㈡槸鍚﹀簲璇ョ敓鎴?
function checkCategoryShouldGenerate(category, config) {
    if (category === 'popular') {
        return config.display_settings?.homepage?.popular_games_display?.enabled !== false;
    }
    if (category === 'new') {
        return config.display_settings?.homepage?.new_games_display?.enabled !== false;
    }
    return true;
}

// ===== Sitemap鐢熸垚鍑芥暟 =====

// 鐢熸垚闈欐€侀〉闈itemap
function generateStaticSitemap(config) {
    const sitemapConfig = config.sitemap?.sitemaps?.static;
    if (!sitemapConfig?.enabled) {
        console.log('📄 Static sitemap disabled, skipping...');
        return false;
    }

    const currentDate = new Date().toISOString();
    const enRoot = makeLangRootUrl(config, 'en').replace(/\/$/, '');
    const zhRoot = makeLangRootUrl(config, 'zh').replace(/\/$/, '');

    const popularEnabled = config.display_settings?.homepage?.popular_games_display?.enabled !== false;
    const newEnabled = config.display_settings?.homepage?.new_games_display?.enabled !== false;

    const urls = [
        { loc: enRoot + '/', lastmod: currentDate, changefreq: sitemapConfig.change_freq || 'daily', priority: sitemapConfig.priority?.homepage || '1.0' },
        { loc: zhRoot + '/', lastmod: currentDate, changefreq: sitemapConfig.change_freq || 'daily', priority: sitemapConfig.priority?.homepage || '1.0' }
    ];

    if (popularEnabled) {
        urls.push({ loc: `${enRoot}/popular/`, lastmod: currentDate, changefreq: sitemapConfig.change_freq || "daily", priority: sitemapConfig.priority?.category_pages || "0.8" });
        urls.push({ loc: `${zhRoot}/popular/`, lastmod: currentDate, changefreq: sitemapConfig.change_freq || "daily", priority: sitemapConfig.priority?.category_pages || "0.8" });
    }

    if (newEnabled) {
        urls.push({ loc: `${enRoot}/new/`, lastmod: currentDate, changefreq: sitemapConfig.change_freq || 'daily', priority: sitemapConfig.priority?.category_pages || '0.8' });
        urls.push({ loc: `${zhRoot}/new/`, lastmod: currentDate, changefreq: sitemapConfig.change_freq || 'daily', priority: sitemapConfig.priority?.category_pages || '0.8' });
    }

    const sitemapXml = generateSitemapXml(urls);

    const sitemapsDir = path.join(CONFIG.distDir, 'sitemaps');
    ensureDirectoryExists(sitemapsDir);


    writeFile(path.join(sitemapsDir, 'sitemap-static.xml'), sitemapXml);
    console.log(`Generated: sitemaps/sitemap-static.xml (${urls.length} URLs)`);
    return true;
}
function generatePopularSitemap(config, games) {
    const sitemapConfig = config.sitemap?.sitemaps?.popular;
    const popularEnabled = config.display_settings?.homepage?.popular_games_display?.enabled !== false;
    if (!sitemapConfig?.enabled || !popularEnabled) {
        console.log('📄 Popular Games sitemap disabled, skipping...');
        return false;
    }

    const currentDate = new Date().toISOString();
    const enRoot = makeLangRootUrl(config, 'en').replace(/\/$/, '');
    const zhRoot = makeLangRootUrl(config, 'zh').replace(/\/$/, '');

    const popularGames = games.filter(game =>
        game.category === 'popular' && checkGameShouldGenerate(game, config)
    );

    const urls = popularGames.flatMap(game => ([
        { loc: `${enRoot}/popular_games/${game.id}.html`, lastmod: currentDate, changefreq: sitemapConfig.change_freq || 'weekly', priority: sitemapConfig.priority || '0.7' },
        { loc: `${zhRoot}/popular_games/${game.id}.html`, lastmod: currentDate, changefreq: sitemapConfig.change_freq || 'weekly', priority: sitemapConfig.priority || '0.7' }
    ]));

    if (urls.length === 0) {
        console.log('📄 No Popular Games found, skipping sitemap generation...');
        return false;
    }

    const sitemapXml = generateSitemapXml(urls);

    const sitemapsDir = path.join(CONFIG.distDir, 'sitemaps');
    ensureDirectoryExists(sitemapsDir);

    writeFile(path.join(sitemapsDir, 'sitemap-popular.xml'), sitemapXml);
    console.log(`Generated: sitemaps/sitemap-popular.xml (${urls.length} games)`);
    return true;
}

// 鐢熸垚New Games sitemap
function generateNewSitemap(config, games) {
    const sitemapConfig = config.sitemap?.sitemaps?.new;
    const newEnabled = config.display_settings?.homepage?.new_games_display?.enabled !== false;

    if (!sitemapConfig?.enabled || !newEnabled) {
        console.log('馃搫 New Games sitemap disabled, skipping...');
        return false;
    }

    const currentDate = new Date().toISOString();
    const enRoot = makeLangRootUrl(config, 'en').replace(/\/$/, '');
    const zhRoot = makeLangRootUrl(config, 'zh').replace(/\/$/, '');

    // 鍙寘鍚疄闄呯敓鎴愮殑New Games锛堜娇鐢ㄧ浉鍚岀殑妫€鏌ラ€昏緫锛?
    const newGames = games.filter(game =>
        game.category === 'new' && checkGameShouldGenerate(game, config)
    );

    const urls = newGames.flatMap(game => ([
        { loc: `${enRoot}/new_games/${game.id}.html`, lastmod: currentDate, changefreq: sitemapConfig.change_freq || "weekly", priority: sitemapConfig.priority || "0.6" },
        { loc: `${zhRoot}/new_games/${game.id}.html`, lastmod: currentDate, changefreq: sitemapConfig.change_freq || "weekly", priority: sitemapConfig.priority || "0.6" }
    ]));

    if (urls.length === 0) {
        console.log('馃搫 No New Games found, skipping sitemap generation...');
        return false;
    }

    const sitemapXml = generateSitemapXml(urls);

    // 纭繚sitemaps鐩綍瀛樺湪
    const sitemapsDir = path.join(CONFIG.distDir, 'sitemaps');
    ensureDirectoryExists(sitemapsDir);

    writeFile(path.join(sitemapsDir, 'sitemap-new.xml'), sitemapXml);
    console.log(`鉁?Generated: sitemaps/sitemap-new.xml (${urls.length} games)`);
    return true;
}

// 鐢熸垚娉曞緥椤甸潰sitemap
function generateLegalSitemap(config) {
    const sitemapConfig = config.sitemap?.sitemaps?.legal;
    if (!sitemapConfig?.enabled) {
        console.log('馃搫 Legal sitemap disabled, skipping...');
        return false;
    }

    const currentDate = new Date().toISOString();
    const enRoot = makeLangRootUrl(config, 'en').replace(/\/$/, '');
    const zhRoot = makeLangRootUrl(config, 'zh').replace(/\/$/, '');

    const legalPages = [
        'Privacy-Policy.html',
        'Contact-Us.html',
        'About-Us.html',
        'DMCA.html'
    ];

    const urls = legalPages.flatMap(page => ([
        { loc: `${enRoot}/legal_info/${page}`, lastmod: currentDate, changefreq: sitemapConfig.change_freq || "yearly", priority: sitemapConfig.priority || "0.3" },
        { loc: `${zhRoot}/legal_info/${page}`, lastmod: currentDate, changefreq: sitemapConfig.change_freq || "yearly", priority: sitemapConfig.priority || "0.3" }
    ]));

    const sitemapXml = generateSitemapXml(urls);

    // 纭繚sitemaps鐩綍瀛樺湪
    const sitemapsDir = path.join(CONFIG.distDir, 'sitemaps');
    ensureDirectoryExists(sitemapsDir);

    writeFile(path.join(sitemapsDir, 'sitemap-legal.xml'), sitemapXml);
    console.log(`鉁?Generated: sitemaps/sitemap-legal.xml (${urls.length} pages)`);
    return true;
}

// 鐢熸垚涓籗itemap Index鏂囦欢
function generateSitemapIndex(config, generatedSitemaps) {
    if (!config.sitemap?.generate_index || generatedSitemaps.length === 0) {
        console.log('馃搫 Sitemap index disabled or no sitemaps generated, skipping...');
        return false;
    }

    const baseUrl = config.sitemap?.base_url || 'https://gameport.example.com';
    const currentDate = new Date().toISOString();

    const sitemapEntries = generatedSitemaps.map(sitemapFile => `
    <sitemap>
        <loc>${baseUrl}/sitemaps/${sitemapFile}</loc>
        <lastmod>${currentDate}</lastmod>
    </sitemap>`).join('');

    const sitemapIndexXml = `<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">${sitemapEntries}
</sitemapindex>`;

    writeFile(path.join(CONFIG.distDir, 'sitemap.xml'), sitemapIndexXml);
    console.log(`鉁?Generated: sitemap.xml (${generatedSitemaps.length} sitemaps)`);
    return true;
}

// 鐢熸垚robots.txt鏂囦欢
function generateRobotsTxt(config) {
    if (!config.sitemap?.enabled) {
        console.log('馃搫 Sitemap disabled, skipping robots.txt generation...');
        return false;
    }

    const baseUrl = config.sitemap?.base_url || 'https://gameport.example.com';

    const robotsTxt = `User-agent: *
Allow: /

# Sitemap
Sitemap: ${baseUrl}/sitemap.xml

# Disallow admin or sensitive areas (if any)
# Disallow: /admin/
# Disallow: /private/`;

    writeFile(path.join(CONFIG.distDir, 'robots.txt'), robotsTxt);
    console.log('鉁?Generated: robots.txt');
    return true;
}

// 鐢熸垚404椤甸潰
function generate404Page(config, gamesData) {
    console.log('馃搫 Generating 404 page...');

    // 璇诲彇404妯℃澘
    const template404 = readFile(path.join(CONFIG.templatesDir, '404.html'));
    if (!template404) {
        console.error('鉂?404 template not found!');
        return false;
    }

    // 妫€鏌ュ姛鑳藉紑鍏崇姸鎬?
    const newGamesEnabled = config.display_settings?.homepage?.new_games_display?.enabled !== false;
    const popularGamesEnabled = config.display_settings?.homepage?.popular_games_display?.enabled !== false;

    // 鐢熸垚瀵艰埅閾炬帴CSS绫?
    const newGamesNavClass = newGamesEnabled ? 'nav-link' : 'nav-link nav-disabled';
    const popularGamesNavClass = popularGamesEnabled ? 'nav-link' : 'nav-link nav-disabled';

    // 鐢熸垚鍔ㄦ€丆SS绫伙紙涓庝富椤典繚鎸佷竴鑷达級
    let dynamicClasses = ['popular-1col', 'iframe-large'];
    if (!newGamesEnabled) {
        dynamicClasses.push('no-new-games');
    }
    if (!popularGamesEnabled) {
        dynamicClasses.push('no-popular-games');
    }
    const dynamicLayoutClasses = dynamicClasses.join(' ');

    // 鏇挎崲妯℃澘鍗犱綅绗?
    // 鏍囩
    const labels = getDisplayLabels(config);

    let page404 = template404
        .replace(/\{\{PAGE_TITLE\}\}/g, `404 - Page Not Found | ${config.site_name || 'GamePort'}`)
        .replace(/\{\{PAGE_DESCRIPTION\}\}/g, 'Sorry, the page you are looking for doesn\'t exist. Browse our collection of games or return to the homepage.')
        .replace(/\{\{PAGE_KEYWORDS\}\}/g, `404, page not found, error, ${config.site_name || 'GamePort'}`)
        .replace(/\{\{SITE_AUTHOR\}\}/g, config.seo?.author || 'GamePort Team')
        .replace(/\{\{SITE_URL\}\}/g, config.site_url || 'https://gameport.example.com')
        .replace(/\{\{SITE_NAME\}\}/g, config.site_name || 'GamePort')
        .replace(/\{\{CANONICAL_URL\}\}/g, (config.seo?.canonical_base_url || config.site_url || 'https://gameport.example.com').replace(/\/$/, '') + '/404.html')
        .replace(/\{\{THEME_CSS_PATH\}\}/g, `css/themes/theme-${config.selected_theme || 'default'}.css`)
        .replace(/\{\{DYNAMIC_LAYOUT_CLASSES\}\}/g, dynamicLayoutClasses)
        .replace(/\{\{NEW_GAMES_NAV_CLASS\}\}/g, newGamesNavClass)
        .replace(/\{\{POPULAR_GAMES_NAV_CLASS\}\}/g, popularGamesNavClass);

    // 娉ㄥ叆鍙厤缃爣绛撅紙瀵艰埅锛?
    page404 = page404
        .replace(/\{\{LABEL_POPULAR\}\}/g, labels.popular)
        .replace(/\{\{LABEL_NEW\}\}/g, labels.new);

    const alt404 = buildAlternateLinks(config, '404.html');
    page404 = page404.replace(/{{ALTERNATE_HREFLANG_LINKS}}/g, alt404);
    // Inject language switch URL (404 page)
    const switch404 = buildSwitchUrl(config, '/404.html');
    page404 = page404.replace(/{{LANG_SWITCH_URL}}/g, switch404);
    page404 = i18nApply(page404);
    writeFile(path.join(CONFIG.distDir, '404.html'), page404);
    console.log('�?Generated: 404.html');

    // 鐢熸垚Cloudflare Pages _redirects鏂囦欢
    const redirectsContent = `# Cloudflare Pages 閲嶅畾鍚戣鍒?
# 404椤甸潰閰嶇疆
/* /404.html 404`;

    writeFile(path.join(CONFIG.distDir, '_redirects'), redirectsContent);
    console.log('鉁?Generated: _redirects (Cloudflare Pages 404 config)');

    return true;
}

// 鐢熸垚sitemap XML鍐呭
function generateSitemapXml(urls) {
    const urlEntries = urls.map(url => `
    <url>
        <loc>${url.loc}</loc>
        <lastmod>${url.lastmod}</lastmod>
        <changefreq>${url.changefreq}</changefreq>
        <priority>${url.priority}</priority>
    </url>`).join('');

    return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">${urlEntries}
</urlset>`;
}

// 涓绘瀯寤哄嚱鏁?
function build() {
    console.log('馃殌 Starting GamePort build process...\n');

    // 鍒涘缓杈撳嚭鐩綍
    ensureDirectoryExists(CONFIG.distDir);

    // 澶嶅埗闈欐€佽祫婧?
    console.log('馃搵 Copying static assets...');
    copyDirectory(path.join(CONFIG.srcDir, 'css'), path.join(CONFIG.distDir, 'css'));
    copyDirectory(path.join(CONFIG.srcDir, 'js'), path.join(CONFIG.distDir, 'js'));
    copyDirectory(path.join(CONFIG.srcDir, 'images'), path.join(CONFIG.distDir, 'images'));

    // 娉ㄦ剰锛歭egal_info鐩綍灏嗗湪鍚庨潰鍗曠嫭澶勭悊锛屼笉鍦ㄨ繖閲屽鍒?

    // 璇诲彇娓告垙鏁版嵁鍜屾ā鏉?
    const gamesData = loadGamesData();

    // 鐢熸垚娓告垙鎼滅储鏁版嵁
    generateGamesDataJS(gamesData);

    const mainTemplate = readFile(path.join(CONFIG.templatesDir, '_main-layout-template.html'));
    const gameTemplate = readFile(path.join(CONFIG.templatesDir, 'game-detail-template.html'));

    if (!mainTemplate) {
        console.error('鉂?Main template not found!');
        return;
    }

    if (!gameTemplate) {
        console.error('鉂?Game template not found!');
        return;
    }

    console.log('\n馃幃 Generating pages...');

    // 璇诲彇閰嶇疆鏂囦欢
    
    const config = loadConfig();

    // 浣跨敤鍒楄〃娓告垙杩涜娓告垙閫夋嫨锛堜笉鍖呭惈棣栭〉涓撶敤娓告垙锛?
    const listGames = gamesData.listGames; // 鍙娇鐢ㄥ垪琛ㄦ父鎴?

    // 妫€鏌ュ姛鑳藉紑鍏崇姸鎬?
    const newGamesEnabled = config.display_settings?.homepage?.new_games_display?.enabled !== false;
    const popularGamesEnabled = config.display_settings?.homepage?.popular_games_display?.enabled !== false;

    console.log(`馃幃 New Games enabled: ${newGamesEnabled}`);
    console.log(`馃幃 Popular Games enabled: ${popularGamesEnabled}`);

    // 鏄剧ず灏嗚鐢熸垚鐨勫唴瀹规瑙?
    console.log('\n馃搵 Build Configuration:');
    console.log(`   馃搫 Homepage: Always generated`);
    console.log(`   馃搫 Popular category pages: ${popularGamesEnabled ? 'Enabled' : 'Disabled'}`);
    console.log(`   馃搫 New category pages: ${newGamesEnabled ? 'Enabled' : 'Disabled'}`);
    console.log(`   馃搫 Popular game detail pages: ${popularGamesEnabled ? 'Enabled' : 'Disabled'}`);
    console.log(`   馃搫 New game detail pages: ${newGamesEnabled ? 'Enabled' : 'Disabled'}`);
    console.log(`   馃椇锔?Sitemap generation: ${config.sitemap?.enabled !== false ? 'Enabled' : 'Disabled'}`);

    // 鏍规嵁閰嶇疆閫夋嫨瑕佹樉绀虹殑娓告垙锛堝彧鏈夊湪enabled鏃舵墠鐢熸垚锛?
    const selectedPopularGames = popularGamesEnabled ? selectGamesForDisplay(
        listGames,
        config.display_settings?.homepage?.popular_games_display,
        'popular'
    ) : [];

    const selectedNewGames = newGamesEnabled ? selectGamesForDisplay(
        listGames,
        config.display_settings?.homepage?.new_games_display,
        'new'
    ) : [];

    // 鐢熸垚娓告垙鍒楄〃HTML锛堝鏋滅鐢ㄥ垯杩斿洖绌哄瓧绗︿覆锛?
    const popularGamesList = popularGamesEnabled ?
        selectedPopularGames.map(game => generateGameCard(game)).join('\n') : '';
    const newGamesList = newGamesEnabled ?
        selectedNewGames.map(game => generateGameCard(game)).join('\n') : '';

    console.log(`馃幆 Homepage Popular Games: ${popularGamesEnabled ? selectedPopularGames.length + ' games' : 'disabled'}`);
    console.log(`馃幆 Homepage New Games: ${newGamesEnabled ? selectedNewGames.length + ' games' : 'disabled'}`);

    // 鐢熸垚涓婚〉锛堜娇鐢ㄩ椤典笓鐢ㄦ父鎴忔暟鎹級
    generateHomePage(mainTemplate, gamesData.homepageGame, popularGamesList, newGamesList, config);

    // 鐢熸垚鍒楄〃椤甸潰锛堜粎涓哄惎鐢ㄧ殑娓告垙绫诲瀷鐢熸垚锛?
    if (checkCategoryShouldGenerate('popular', config)) {
        generateListPage(gamesData.listGames, 'popular', config);
        console.log('鉁?Generated: popular category pages');
    } else {
        console.log('鈴笍 Skipped: popular category pages (Popular Games disabled)');
    }

    if (checkCategoryShouldGenerate('new', config)) {
        generateListPage(gamesData.listGames, 'new', config);
        console.log('鉁?Generated: new category pages');
    } else {
        console.log('鈴笍 Skipped: new category pages (New Games disabled)');
    }

    // 鐢熸垚娓告垙椤甸潰锛堜粎涓哄惎鐢ㄧ殑娓告垙绫诲瀷鐢熸垚璇︽儏椤碉級
    let generatedGamesCount = 0;
    let skippedGamesCount = 0;

    gamesData.listGames.forEach(game => {
        if (checkGameShouldGenerate(game, config)) {
            generateGamePage(gameTemplate, game, popularGamesList, newGamesList, config);
            generatedGamesCount++;
        } else {
            console.log(`鈴笍 Skipped: ${game.category}/${game.id}.html (${game.category} games disabled)`);
            skippedGamesCount++;
        }
    });

    console.log(`馃幃 Generated ${generatedGamesCount} game pages, skipped ${skippedGamesCount} pages`);

    // 鐢熸垚娉曞緥淇℃伅椤甸潰
    generateLegalPages(config);

    // 鐢熸垚404椤甸潰
    generate404Page(config, gamesData);

    // ========== 按语言再生成一套（中文 /zh/） ==========
    try {
        const langs = Array.isArray(config.i18n?.languages) ? config.i18n.languages : [];
        if (langs.includes('zh')) {
            const baseDist = CONFIG.distDir;
            const zhPath = (config.i18n?.paths?.zh || '/zh/').replace(/^\/+|\/+$/g, '');
            const zhDistDir = zhPath ? path.join(baseDist, zhPath) : path.join(baseDist, 'zh');

            setI18nContext('zh', 'en');
            CONFIG.distDir = zhDistDir;
            ensureDirectoryExists(CONFIG.distDir);

            console.log(`📋 Copying static assets for zh -> ${CONFIG.distDir} ...`);
            copyDirectory(path.join(CONFIG.srcDir, 'css'), path.join(CONFIG.distDir, 'css'));
            copyDirectory(path.join(CONFIG.srcDir, 'js'), path.join(CONFIG.distDir, 'js'));
            copyDirectory(path.join(CONFIG.srcDir, 'images'), path.join(CONFIG.distDir, 'images'));

            generateGamesDataJS(gamesData);

            console.log('\n🎮 Generating pages for zh...');

            const newGamesEnabledZH = config.display_settings?.homepage?.new_games_display?.enabled !== false;
            const popularGamesEnabledZH = config.display_settings?.homepage?.popular_games_display?.enabled !== false;

            const zhSelectedPopularGames = popularGamesEnabledZH ? selectGamesForDisplay(
                listGames,
                config.display_settings?.homepage?.popular_games_display,
                'popular'
            ) : [];
            const zhSelectedNewGames = newGamesEnabledZH ? selectGamesForDisplay(
                listGames,
                config.display_settings?.homepage?.new_games_display,
                'new'
            ) : [];

            const zhPopularGamesList = popularGamesEnabledZH ? zhSelectedPopularGames.map(game => generateGameCard(game)).join('\n') : '';
            const zhNewGamesList = newGamesEnabledZH ? zhSelectedNewGames.map(game => generateGameCard(game)).join('\n') : '';

            generateHomePage(mainTemplate, gamesData.homepageGame, zhPopularGamesList, zhNewGamesList, config);
            if (checkCategoryShouldGenerate('popular', config)) {
                generateListPage(gamesData.listGames, 'popular', config);
            }
            if (checkCategoryShouldGenerate('new', config)) {
                generateListPage(gamesData.listGames, 'new', config);
            }
            gamesData.listGames.forEach(game => {
                if (checkGameShouldGenerate(game, config)) {
                    generateGamePage(gameTemplate, game, zhPopularGamesList, zhNewGamesList, config);
                }
            });

            generateLegalPages(config);
            generate404Page(config, gamesData);

            CONFIG.distDir = baseDist;
            setI18nContext(defaultLang, 'en');
        }
    } catch (e) {
        console.warn('⚠️ Multi-language build fallback:', e.message);
    }
    // 鐢熸垚Sitemap鏂囦欢
    if (config.sitemap?.enabled !== false) {
        console.log('\n馃椇锔?Generating sitemaps...');
        const generatedSitemaps = [];

        // 鐢熸垚鍚勪釜瀛恠itemap
        if (generateStaticSitemap(config)) {
            generatedSitemaps.push('sitemap-static.xml');
        }

        if (generatePopularSitemap(config, gamesData.listGames)) {
            generatedSitemaps.push('sitemap-popular.xml');
        }

        if (generateNewSitemap(config, gamesData.listGames)) {
            generatedSitemaps.push('sitemap-new.xml');
        }

        if (generateLegalSitemap(config)) {
            generatedSitemaps.push('sitemap-legal.xml');
        }

        // 鐢熸垚涓籹itemap index鏂囦欢
        generateSitemapIndex(config, generatedSitemaps);

        // 鐢熸垚robots.txt
        generateRobotsTxt(config);

        console.log(`馃椇锔?Sitemap generation completed! Generated ${generatedSitemaps.length} sitemaps.`);
    } else {
        console.log('馃椇锔?Sitemap generation disabled in config.');
    }

    console.log('\n馃帀 Build completed successfully!');
    console.log(`馃搧 Output directory: ${CONFIG.distDir}`);
}

// 鐢熸垚涓婚〉
function generateHomePage(template, homepageGame, popularGamesList, newGamesList, config) {
    // 妫€鏌ュ姛鑳藉紑鍏崇姸鎬?
    const newGamesEnabled = config.display_settings?.homepage?.new_games_display?.enabled !== false;
    const popularGamesEnabled = config.display_settings?.homepage?.popular_games_display?.enabled !== false;

    // 鑾峰彇 Popular Games 鍒楁暟閰嶇疆
    const popularColumns = config.display_settings?.homepage?.popular_games_display?.columns || 1;
    const popularColumnsClass = `popular-${popularColumns}col`;

    // 鑾峰彇 iframe 灏哄閰嶇疆
    const iframeSize = config.iframe_settings?.default_size || 'small';
    const globalIframeConfig = config.iframe_settings?.sizes?.[iframeSize] || config.iframe_settings?.sizes?.small;

    // 鑾峰彇棣栭〉娓告垙鐨勬湁鏁坕frame閰嶇疆锛堟父鎴忕骇閰嶇疆浼樺厛锛?
    const effectiveIframeConfig = getGameIframeConfig(homepageGame, globalIframeConfig);

    // 鏍规嵁鏈夋晥鐨刬frame瀹藉害閲嶆柊閫夋嫨甯冨眬绫伙紝閬垮厤琚粯璁arge闄愬埗鍒?60
    const iframeSizeClass = getIframeSizeClassByWidth(effectiveIframeConfig.width);

    // 鐢熸垚鍔ㄦ€丆SS绫?
    let dynamicClasses = [popularColumnsClass, iframeSizeClass];
    if (!newGamesEnabled) {
        dynamicClasses.push('no-new-games');
    }
    if (!popularGamesEnabled) {
        dynamicClasses.push('no-popular-games');
    }

    // 褰搃frame瀹藉害澶т簬1000px鏃讹紝浣跨敤鐗规畩甯冨眬锛圥opular Games绉诲埌鏈€鍚庯級
    if (effectiveIframeConfig.width > 1000) {
        dynamicClasses.push('wide-iframe-layout');
    }

    const dynamicLayoutClasses = dynamicClasses.join(' ').replace('iframe-iframe', 'iframe-');

    // 鐢熸垚瀵艰埅閾炬帴CSS绫?
    const newGamesNavClass = newGamesEnabled ? 'nav-link' : 'nav-link nav-disabled';
    const popularGamesNavClass = popularGamesEnabled ? 'nav-link' : 'nav-link nav-disabled';

    console.log(`馃幆 Popular Games columns: ${popularColumns} (class: ${popularColumnsClass})`);
    console.log(`馃柤锔?iframe size: ${iframeSize} (${globalIframeConfig?.width}脳${globalIframeConfig?.height}) (class: ${iframeSizeClass})`);
    console.log(`馃幃 Effective iframe size: ${effectiveIframeConfig.width}脳${effectiveIframeConfig.height}`);

    // 鐢熸垚棣栭〉涓撶敤娓告垙鐨刦eature blocks鍐呭

    const featureContent = homepageGame ? generateFeatureBlocks(localizeFeatureBlocks(homepageGame.feature_blocks, I18N_CTX.lang)) :
        '<div class="welcome-message"><h2>Welcome to GamePort</h2><p>Select a game to start playing!</p></div>';

    // 鏉′欢娓叉煋娓告垙鍖哄煙HTML
    let processedTemplate = template;

    // 澶勭悊New Games鍖哄煙
    if (!newGamesEnabled) {
        // 绉婚櫎New Games Section
        processedTemplate = processedTemplate.replace(
            /<!-- New Games Section -->[\s\S]*?<\/section>/,
            ''
        );
    }

    // 澶勭悊Popular Games鍖哄煙锛堢Щ鍔ㄧ鍜屼晶杈规爮锛?
    if (!popularGamesEnabled) {
        // 绉婚櫎Popular Games Section (Mobile Only)
        processedTemplate = processedTemplate.replace(
            /<!-- Popular Games Section \(Mobile Only\) -->[\s\S]*?<\/section>/,
            ''
        );
        // 绉婚櫎Right Sidebar涓殑Popular Games Section
        processedTemplate = processedTemplate.replace(
            /<!-- Popular Games Section -->[\s\S]*?<\/section>/,
            ''
        );
    }

    // FAQ section锛堥椤碉級
    const faqSection = generateFAQSection(config, 'home');

    // 鏍囩
    const labels = getDisplayLabels(config);

    const canonRoot = (I18N_CTX.lang === 'zh' ? makeLangRootUrl(config, 'zh') : makeLangRootUrl(config, 'en'));

    let homePage = processedTemplate
        .replace(/\{\{GAME_URL\}\}/g, homepageGame ? homepageGame.gameUrl : 'about:blank')
        .replace(/\{\{GAME_ID\}\}/g, homepageGame ? homepageGame.id : 'homepage-default')
        .replace(/\{\{GAME_TITLE\}\}/g, homepageGame ? homepageGame.name : 'Welcome to GamePort')
        .replace(/\{\{GAME_THUMBNAIL\}\}/g, homepageGame ? homepageGame.thumbnail : 'images/common/default-game.png')
        .replace(/\{\{POPULAR_GAMES_LIST\}\}/g, popularGamesList)
        .replace(/\{\{NEW_GAMES_LIST\}\}/g, newGamesList)
        .replace(/\{\{GAME_FEATURE_CONTENT\}\}/g, featureContent)
        .replace(/\{\{PAGE_TITLE\}\}/g, config.seo?.default_title || (homepageGame && homepageGame.meta ? homepageGame.meta.title : 'GamePort - Your Ultimate Game Destination'))
        .replace(/\{\{PAGE_DESCRIPTION\}\}/g, config.seo?.default_description || (homepageGame && homepageGame.meta ? homepageGame.meta.description : 'Discover and play the best online games for free!'))
        .replace(/\{\{PAGE_KEYWORDS\}\}/g, config.seo?.keywords || 'online games, free games, browser games')
        .replace(/\{\{SITE_AUTHOR\}\}/g, config.seo?.author || 'GamePort Team')
        .replace(/\{\{SITE_URL\}\}/g, config.site_url || 'https://gameport.example.com')
        .replace(/\{\{SITE_NAME\}\}/g, config.site_name || 'GamePort')
        .replace(/\{\{CANONICAL_URL\}\}/g, canonRoot)
        .replace(/\{\{THEME_CSS_PATH\}\}/g, `css/themes/theme-${config.selected_theme || 'default'}.css`)
        .replace(/\{\{POPULAR_COLUMNS_CLASS\}\}/g, popularColumnsClass)
        .replace(/\{\{IFRAME_SIZE_CLASS\}\}/g, iframeSizeClass)
        .replace(/\{\{IFRAME_WIDTH\}\}/g, effectiveIframeConfig.width)
        .replace(/\{\{IFRAME_HEIGHT\}\}/g, effectiveIframeConfig.height)
        .replace(/\{\{DYNAMIC_LAYOUT_CLASSES\}\}/g, dynamicLayoutClasses)
        .replace(/\{\{NEW_GAMES_NAV_CLASS\}\}/g, newGamesNavClass)
        .replace(/\{\{POPULAR_GAMES_NAV_CLASS\}\}/g, popularGamesNavClass)
        .replace(/\{\{HOME_ACTIVE\}\}/g, '')
        .replace(/\{\{POPULAR_ACTIVE\}\}/g, '')
        .replace(/\{\{NEW_ACTIVE\}\}/g, '')
        .replace(/\{\{FAQ_SECTION\}\}/g, faqSection);

    // 娉ㄥ叆鍙厤缃爣绛?
    homePage = homePage
        .replace(/\{\{LABEL_POPULAR\}\}/g, labels.popular)
        .replace(/\{\{LABEL_NEW\}\}/g, labels.new);

    const altLinks = buildAlternateLinks(config, '');
    homePage = homePage.replace(/\{\{ALTERNATE_HREFLANG_LINKS\}\}/g, altLinks);
    // Inject language switch URL (home)
    const homeSwitchUrl = buildSwitchUrl(config, '');
    homePage = homePage.replace(/\{\{LANG_SWITCH_URL\}\}/g, homeSwitchUrl);

    homePage = i18nApply(homePage);
    writeFile(path.join(CONFIG.distDir, 'index.html'), homePage);
}

// 鐢熸垚鍒嗛〉瀵艰埅HTML
function generatePaginationHTML(currentPage, totalPages, category) {
    if (totalPages <= 1) {
        return ''; // 涓嶆樉绀哄垎椤靛鑸?
    }

    let paginationHTML = `
    <nav class="pagination-nav" aria-label="${category} games pagination">
        <div class="container">
            <div class="pagination">`;

    // Previous鎸夐挳
    if (currentPage > 1) {
        const prevUrl = currentPage === 2 ? 'index.html' : `page-${currentPage - 1}.html`;
        paginationHTML += `
                <a href="${prevUrl}" class="pagination-btn pagination-prev">
                    <span>鈥?/span> Previous
                </a>`;
    }

    // 椤电爜鏁板瓧
    paginationHTML += `
                <div class="pagination-numbers">`;

    // 鏄剧ず椤电爜鏁板瓧
    if (totalPages <= 5) {
        // 濡傛灉鎬婚〉鏁?=5椤碉紝鏄剧ず鎵€鏈夐〉鐮?
        for (let i = 1; i <= totalPages; i++) {
            const url = i === 1 ? 'index.html' : `page-${i}.html`;
            const activeClass = i === currentPage ? ' active' : '';
            paginationHTML += `
                    <a href="${url}" class="pagination-number${activeClass}">${i}</a>`;
        }
    } else {
        // 濡傛灉鎬婚〉鏁?>5椤碉紝鏄剧ず鍓?椤?...鍚?椤?
        // 鏄剧ず鍓?椤?
        for (let i = 1; i <= 3; i++) {
            const url = i === 1 ? 'index.html' : `page-${i}.html`;
            const activeClass = i === currentPage ? ' active' : '';
            paginationHTML += `
                    <a href="${url}" class="pagination-number${activeClass}">${i}</a>`;
        }

        // 鏄剧ず鐪佺暐鍙?
        paginationHTML += `
                    <span class="pagination-dots">...</span>`;

        // 鏄剧ず鏈€鍚庝竴椤?
        const lastUrl = `page-${totalPages}.html`;
        const lastActiveClass = totalPages === currentPage ? ' active' : '';
        paginationHTML += `
                    <a href="${lastUrl}" class="pagination-number${lastActiveClass}">${totalPages}</a>`;
    }

    paginationHTML += `
                </div>`;

    // Next鎸夐挳
    if (currentPage < totalPages) {
        const nextUrl = `page-${currentPage + 1}.html`;
        paginationHTML += `
                <a href="${nextUrl}" class="pagination-btn pagination-next">
                    Next <span>鈥?/span>
                </a>`;
    }

    paginationHTML += `
            </div>
        </div>
    </nav>`;

    return paginationHTML;
}

// 鐢熸垚鍒楄〃椤甸潰锛堟敮鎸佸垎椤碉級
function generateListPage(games, category, config) {
    console.log(`馃搫 Generating ${category} pages...`);

    const GAMES_PER_PAGE = 60; // 6鍒?脳 10琛?
    const categoryGames = games.filter(game => game.category === category);
    const totalPages = Math.ceil(categoryGames.length / GAMES_PER_PAGE);

    // 鍒涘缓鍒嗙被鏂囦欢澶?
    const categoryDir = path.join(CONFIG.distDir, category);
    ensureDirectoryExists(categoryDir);

    // 浣跨敤涓撻棬鐨勫垪琛ㄩ〉闈㈡ā鏉?
    const listTemplate = readFile(path.join(CONFIG.templatesDir, '_list-layout-template.html'));

    if (!listTemplate) {
        console.error('鉂?List template not found!');
        return;
    }

    // 璇诲彇閰嶇疆鏂囦欢

    // 鑾峰彇 Popular Games 鍒楁暟閰嶇疆
    const popularColumns = config.display_settings?.homepage?.popular_games_display?.columns || 1;
    const popularColumnsClass = `popular-${popularColumns}col`;

    // 鏍囩
    const labels = getDisplayLabels(config);

    // 浠庨厤缃鍙栧垎绫婚〉鏍囬涓庡壇鏍囬锛堜笉瀛樺湪鏃跺洖閫€鑷虫爣绛惧拰榛樿鏂囨锛?
    const categoryPagesCfg = config.display_settings?.category_pages || {};
    const pageInfo = {
        'popular': {
            title: categoryPagesCfg.popular?.title || labels.popular,
            subtitle: categoryPagesCfg.popular?.subtitle || 'Play the most popular and trending games. Discover top-rated games loved by millions of players worldwide.',
            activeClass: 'active'
        },
        'new': {
            title: categoryPagesCfg.new?.title || labels.new,
            subtitle: categoryPagesCfg.new?.subtitle || 'Discover the latest and newest games added to GameWeb. Fresh content updated regularly with trending games and exciting new releases.',
            activeClass: 'active'
        }
    };

    const info = pageInfo[category];
    if (!info) {
        console.error(`鉂?Unknown category: ${category}`);
        return;
    }

    // 鐢熸垚姣忎竴椤?
    for (let page = 1; page <= totalPages; page++) {
        const startIndex = (page - 1) * GAMES_PER_PAGE;
        const endIndex = startIndex + GAMES_PER_PAGE;
        const pageGames = categoryGames.slice(startIndex, endIndex);

        // 鏂囦欢鍚嶈鍒?
        const fileName = page === 1 ? 'index.html' : `page-${page}.html`;
        const filePath = path.join(categoryDir, fileName);

        // 鐢熸垚娓告垙鍒楄〃HTML锛堜慨姝ｇ浉瀵硅矾寰勶級
        let categoryGamesList = pageGames.map(game => generateGameCard(game)).join('\n');

        // 淇鍒嗙被椤甸潰涓殑娓告垙鍗＄墖閾炬帴璺緞
        categoryGamesList = categoryGamesList
            .replace(/href="popular_games\//g, 'href="../popular_games/')
            .replace(/href="new_games\//g, 'href="../new_games/')
            .replace(/src="images\//g, 'src="../images/');

        // 鐢熸垚鍒嗛〉瀵艰埅HTML
        const paginationHTML = generatePaginationHTML(page, totalPages, category);

        // 椤甸潰鏍囬锛堝寘鍚〉鐮侊級
        const pageTitle = page === 1 ? info.title : `${info.title} - Page ${page}`;
        const pageDescription = page === 1 ? info.subtitle : `${info.subtitle} Browse page ${page} of ${totalPages}.`;
        // Canonical & hreflang (per-language)
        const isIndex = fileName === 'index.html';
        const relPath = isIndex ? `${category}/` : `${category}/${fileName}`;
        const canonBaseList = (I18N_CTX.lang === 'zh' ? makeLangRootUrl(config, 'zh') : makeLangRootUrl(config, 'en'));
        const canonicalUrl = canonBaseList.replace(/\/$/, '') + (isIndex ? `/${category}/` : `/${category}/${fileName}`);

        // 鐢熸垚椤甸潰HTML
        let listPage = listTemplate
            .replace(/\{\{PAGE_TITLE\}\}/g, pageTitle)
            .replace(/\{\{PAGE_MAIN_TITLE\}\}/g, info.title)
            .replace(/\{\{PAGE_SUBTITLE\}\}/g, info.subtitle)
            .replace(/\{\{PAGE_DESCRIPTION\}\}/g, pageDescription)
            .replace(/\{\{PAGE_KEYWORDS\}\}/g, `${category} games, online games, free games`)
            .replace(/\{\{SITE_AUTHOR\}\}/g, config.seo?.author || 'GamePort Team')
            .replace(/\{\{SITE_URL\}\}/g, config.site_url || 'https://gameport.example.com')
            .replace(/\{\{CANONICAL_URL\}\}/g, canonicalUrl)
            .replace(/\{\{THEME_CSS_PATH\}\}/g, `../css/themes/theme-${config.selected_theme || 'default'}.css`)
            .replace(/\{\{SITE_NAME\}\}/g, config.site_name || 'GamePort')
            .replace(/\{\{GAMES_LIST\}\}/g, categoryGamesList)
            .replace(/\{\{PAGINATION_HTML\}\}/g, paginationHTML)
            .replace(/\{\{POPULAR_COLUMNS_CLASS\}\}/g, popularColumnsClass)
            .replace(/\{\{BREADCRUMB_CURRENT\}\}/g, info.title)
            .replace(/\{\{HOME_ACTIVE\}\}/g, '')
            .replace(/\{\{POPULAR_ACTIVE\}\}/g, category === 'popular' ? info.activeClass : '')
            .replace(/\{\{NEW_ACTIVE\}\}/g, category === 'new' ? info.activeClass : '');

        // 注入 hreflang alternate 链接（分类页）
        const listAltLinks = buildAlternateLinks(config, relPath);
        listPage = listPage.replace(/\{\{ALTERNATE_HREFLANG_LINKS\}\}/g, listAltLinks);
        // Inject language switch URL (list page)
        const listSwitchUrl = buildSwitchUrl(config, relPath);
        listPage = listPage.replace(/\{\{LANG_SWITCH_URL\}\}/g, listSwitchUrl);

        // 淇鍒嗙被椤甸潰涓殑CSS鍜屽叾浠栬祫婧愯矾寰?
        listPage = listPage
            .replace(/href="css\/style\.css"/g, 'href="../css/style.css"')
            .replace(/href="images\//g, 'href="../images/')
            .replace(/src="images\//g, 'src="../images/')
            .replace(/href="js\//g, 'href="../js/')
            .replace(/src="js\//g, 'src="../js/');

        // 娉ㄥ叆鍙厤缃爣绛撅紙瀵艰埅锛?
        listPage = listPage
            .replace(/\{\{LABEL_POPULAR\}\}/g, labels.popular)
            .replace(/\{\{LABEL_NEW\}\}/g, labels.new);

        listPage = i18nApply(listPage);
        writeFile(filePath, listPage);
        console.log(`鉁?Generated: ${category}/${fileName}`);
    }

    console.log(`馃搳 Generated ${totalPages} pages for ${category} (${categoryGames.length} games, ${GAMES_PER_PAGE} per page)`);
}

// 鐢熸垚娓告垙椤甸潰
function generateGamePage(template, game, popularGamesList, newGamesList, config) {
    const localized = localizeGame(game, I18N_CTX.lang);
    const featureContent = generateFeatureBlocks(localized.feature_blocks);

    // 妫€鏌ュ姛鑳藉紑鍏崇姸鎬?
    const newGamesEnabled = config.display_settings?.homepage?.new_games_display?.enabled !== false;
    const popularGamesEnabled = config.display_settings?.homepage?.popular_games_display?.enabled !== false;

    // 鑾峰彇 Popular Games 鍒楁暟閰嶇疆
    const popularColumns = config.display_settings?.homepage?.popular_games_display?.columns || 1;
    const popularColumnsClass = `popular-${popularColumns}col`;

    // 鑾峰彇 iframe 灏哄閰嶇疆
    const iframeSize = config.iframe_settings?.default_size || 'small';
    const globalIframeConfig = config.iframe_settings?.sizes?.[iframeSize] || config.iframe_settings?.sizes?.small;

    // 鑾峰彇褰撳墠娓告垙鐨勬湁鏁坕frame閰嶇疆锛堟父鎴忕骇閰嶇疆浼樺厛锛?
    const effectiveIframeConfig = getGameIframeConfig(game, globalIframeConfig);

    // 鏍规嵁鏈夋晥鐨刬frame瀹藉害閲嶆柊閫夋嫨甯冨眬绫伙紝閬垮厤琚粯璁arge闄愬埗鍒?60
    const iframeSizeClass = getIframeSizeClassByWidth(effectiveIframeConfig.width);

    // 鐢熸垚鍔ㄦ€丆SS绫?
    let dynamicClasses = [popularColumnsClass, iframeSizeClass];
    if (!newGamesEnabled) {
        dynamicClasses.push('no-new-games');
    }
    if (!popularGamesEnabled) {
        dynamicClasses.push('no-popular-games');
    }

    // 褰搃frame瀹藉害澶т簬1000px鏃讹紝浣跨敤鐗规畩甯冨眬锛圥opular Games绉诲埌鏈€鍚庯級
    if (effectiveIframeConfig.width > 1000) {
        dynamicClasses.push('wide-iframe-layout');
    }

    const dynamicLayoutClasses = dynamicClasses.join(' ').replace('iframe-iframe', 'iframe-');

    // 鐢熸垚瀵艰埅閾炬帴CSS绫?
    const newGamesNavClass = newGamesEnabled ? 'nav-link' : 'nav-link nav-disabled';
    const popularGamesNavClass = popularGamesEnabled ? 'nav-link' : 'nav-link nav-disabled';

    // 纭畾娓告垙椤甸潰鐨勭洰褰?
    const gameDir = game.category === 'popular' ? 'popular_games' : 'new_games';
    const gameDirPath = path.join(CONFIG.distDir, gameDir);

    // 纭繚娓告垙鐩綍瀛樺湪
    ensureDirectoryExists(gameDirPath);

    // 鏉′欢娓叉煋娓告垙鍖哄煙HTML
    let processedTemplate = template;

    // 澶勭悊New Games鍖哄煙
    if (!newGamesEnabled) {
        // 绉婚櫎New Games Section
        processedTemplate = processedTemplate.replace(
            /<!-- New Games Section -->[\s\S]*?<\/section>/,
            ''
        );
    }

    // 澶勭悊Popular Games鍖哄煙锛堢Щ鍔ㄧ鍜屼晶杈规爮锛?
    if (!popularGamesEnabled) {
        // 绉婚櫎Popular Games Section (Mobile Only)
        processedTemplate = processedTemplate.replace(
            /<!-- Popular Games Section \(Mobile Only\) -->[\s\S]*?<\/section>/,
            ''
        );
        // 绉婚櫎Right Sidebar涓殑Popular Games Section
        processedTemplate = processedTemplate.replace(
            /<!-- Popular Games Section -->[\s\S]*?<\/section>/,
            ''
        );
    }

    // 鏇挎崲鎵€鏈夊崰浣嶇
    // FAQ section锛堟父鎴忚鎯呴〉锛?
    const faqSection = generateFAQSection(config, 'game');

    // 鏍囩
    const labels = getDisplayLabels(config);

    // Canonical & hreflang for detail page
    const relDetailPath = `${gameDir}/${game.id}.html`;
    const canonBaseDetail = (I18N_CTX.lang === 'zh' ? makeLangRootUrl(config, 'zh') : makeLangRootUrl(config, 'en'));
    const canonicalDetailUrl = canonBaseDetail.replace(/\/$/, '') + `/${relDetailPath}`;

    let gamePage = processedTemplate
        .replace(/\{\{GAME_URL\}\}/g, game.gameUrl || 'about:blank')
        .replace(/\{\{GAME_ID\}\}/g, game.id)
        .replace(/\{\{GAME_TITLE\}\}/g, game.name)
        .replace(/\{\{GAME_THUMBNAIL\}\}/g, game.thumbnail || 'images/common/default-game.png')
        .replace(/\{\{POPULAR_GAMES_LIST\}\}/g, popularGamesList)
        .replace(/\{\{NEW_GAMES_LIST\}\}/g, newGamesList)
        .replace(/\{\{GAME_FEATURE_CONTENT\}\}/g, featureContent)
        .replace(/\{\{PAGE_TITLE\}\}/g, localized.meta ? localized.meta.title : `${game.name} - Play Online Free | GamePort`)
        .replace(/\{\{PAGE_DESCRIPTION\}\}/g, localized.meta ? localized.meta.description : `Play ${game.name} online for free at GamePort. No downloads required!`)
        .replace(/\{\{PAGE_KEYWORDS\}\}/g, `${game.name}, online game, free game, ${game.category}`)
        .replace(/\{\{SITE_AUTHOR\}\}/g, config.seo?.author || 'GamePort Team')
        .replace(/\{\{SITE_URL\}\}/g, config.site_url || 'https://gameport.example.com')
        .replace(/\{\{SITE_NAME\}\}/g, config.site_name || 'GamePort')
        .replace(/\{\{CANONICAL_URL\}\}/g, canonicalDetailUrl)
        .replace(/\{\{THEME_CSS_PATH\}\}/g, `../css/themes/theme-${config.selected_theme || 'default'}.css`)
        .replace(/\{\{POPULAR_COLUMNS_CLASS\}\}/g, popularColumnsClass)
        .replace(/\{\{IFRAME_SIZE_CLASS\}\}/g, iframeSizeClass)
        .replace(/\{\{IFRAME_WIDTH\}\}/g, effectiveIframeConfig.width)
        .replace(/\{\{IFRAME_HEIGHT\}\}/g, effectiveIframeConfig.height)
        .replace(/\{\{DYNAMIC_LAYOUT_CLASSES\}\}/g, dynamicLayoutClasses)
        .replace(/\{\{NEW_GAMES_NAV_CLASS\}\}/g, newGamesNavClass)
        .replace(/\{\{POPULAR_GAMES_NAV_CLASS\}\}/g, popularGamesNavClass)
        .replace(/\{\{HOME_ACTIVE\}\}/g, '')
        .replace(/\{\{POPULAR_ACTIVE\}\}/g, '')
        .replace(/\{\{NEW_ACTIVE\}\}/g, '')
        .replace(/\{\{FAQ_SECTION\}\}/g, faqSection);

    // Inject hreflang alternate links (detail page)
    const detailAltLinks = buildAlternateLinks(config, relDetailPath);


    // Inject language switch URL (detail page)
    const detailSwitchUrl = buildSwitchUrl(config, relDetailPath);
    // 鐒跺悗淇娓告垙椤甸潰鐨勭浉瀵硅矾寰?
    gamePage = gamePage
        .replace(/\{\{HOME_ACTIVE\}\}/g, '')
        .replace(/\{\{POPULAR_ACTIVE\}\}/g, '')
        .replace(/\{\{NEW_ACTIVE\}\}/g, '');

    // 鐒跺悗淇娓告垙椤甸潰鐨勭浉瀵硅矾寰?
    gamePage = gamePage
        .replace(/href="index\.html"/g, 'href="../index.html"')
        .replace(/href="popular\/"/g, 'href="../popular/"')
        .replace(/href="new\/"/g, 'href="../new/"')
        .replace(/src="images\//g, 'src="../images/')
        .replace(/href="css\/style\.css"/g, 'href="../css/style.css"')
        .replace(/href="popular_games\//g, 'href="../popular_games/')
        .replace(/href="new_games\//g, 'href="../new_games/');

    // 娉ㄥ叆鍙厤缃爣绛?
    gamePage = gamePage
        .replace(/\{\{LABEL_POPULAR\}\}/g, labels.popular)
        .replace(/\{\{LABEL_NEW\}\}/g, labels.new);

    gamePage = i18nApply(gamePage);
    writeFile(path.join(gameDirPath, `${game.id}.html`), gamePage);
    console.log(`鉁?Generated: dist\\${gameDir}\\${game.id}.html`);
}

// 澶嶅埗娉曞緥淇℃伅椤甸潰
function generateLegalPages(config) {
    console.log('馃搫 Copying legal pages...');

    // 鍒涘缓legal_info鐩綍
    const legalDir = path.join(CONFIG.distDir, 'legal_info');
    ensureDirectoryExists(legalDir);

    // 澶嶅埗骞跺鐞哖rivacy Policy椤甸潰
    copyAndProcessLegalPage(legalDir, config);
}

// 澶嶅埗骞跺鐞嗘硶寰嬮〉闈?
function copyAndProcessLegalPage(legalDir, config) {
    // 璇诲彇閰嶇疆鏂囦欢
    const srcLegalDir = path.join(CONFIG.srcDir, 'legal_info');

    // 澶勭悊Privacy Policy椤甸潰
    const srcPrivacyPath = path.join(srcLegalDir, 'Privacy-Policy.html');
    if (fs.existsSync(srcPrivacyPath)) {
        let privacyContent = readFile(srcPrivacyPath);
        privacyContent = processLegalPageContent(privacyContent, config, 'Privacy-Policy.html');
        const destPrivacyPath = path.join(legalDir, 'Privacy-Policy.html');
        writeFile(destPrivacyPath, privacyContent);
        console.log(`鉁?Copied and processed: legal_info/Privacy-Policy.html`);
    } else {
        console.warn(`鈿狅笍 Source Privacy Policy file not found: ${srcPrivacyPath}`);
    }

    // 澶勭悊Contact Us椤甸潰
    const srcContactPath = path.join(srcLegalDir, 'Contact-Us.html');
    if (fs.existsSync(srcContactPath)) {
        let contactContent = readFile(srcContactPath);
        contactContent = processLegalPageContent(contactContent, config, 'Contact-Us.html');
        const destContactPath = path.join(legalDir, 'Contact-Us.html');
        writeFile(destContactPath, contactContent);
        console.log(`鉁?Copied and processed: legal_info/Contact-Us.html`);
    } else {
        console.warn(`鈿狅笍 Source Contact Us file not found: ${srcContactPath}`);
    }

    // 澶勭悊About Us椤甸潰
    const srcAboutPath = path.join(srcLegalDir, 'About-Us.html');
    if (fs.existsSync(srcAboutPath)) {
        let aboutContent = readFile(srcAboutPath);
        aboutContent = processLegalPageContent(aboutContent, config, 'About-Us.html');
        const destAboutPath = path.join(legalDir, 'About-Us.html');
        writeFile(destAboutPath, aboutContent);
        console.log(`鉁?Copied and processed: legal_info/About-Us.html`);
    } else {
        console.warn(`鈿狅笍 Source About Us file not found: ${srcAboutPath}`);
    }

    // 澶勭悊DMCA椤甸潰
    const srcDmcaPath = path.join(srcLegalDir, 'DMCA.html');
    if (fs.existsSync(srcDmcaPath)) {
        let dmcaContent = readFile(srcDmcaPath);
        dmcaContent = processLegalPageContent(dmcaContent, config, 'DMCA.html');
        const destDmcaPath = path.join(legalDir, 'DMCA.html');
        writeFile(destDmcaPath, dmcaContent);
        console.log(`鉁?Copied and processed: legal_info/DMCA.html`);
    } else {
        console.warn(`鈿狅笍 Source DMCA file not found: ${srcDmcaPath}`);
    }
}

// 澶勭悊娉曞緥椤甸潰鍐呭鐨勯€氱敤鍑芥暟
function processLegalPageContent(content, config, fileName) {
    return content
        .replace(/\{\{SITE_NAME\}\}/g, config.site_name || 'GamePort')
        .replace(/\{\{SITE_AUTHOR\}\}/g, config.seo?.author || 'GamePort Team')
        .replace(/\{\{SITE_URL\}\}/g, config.site_url || 'https://gameport.example.com')
        .replace(/\{\{CANONICAL_URL\}\}/g, (config.seo?.canonical_base_url || config.site_url || 'https://gameport.example.com').replace(/\/$/, '') + `/legal_info/${fileName}`)
        .replace(/\{\{THEME_CSS_PATH\}\}/g, `../css/themes/theme-${config.selected_theme || 'default'}.css`)
        .replace(/\{\{CONTACT_EMAIL\}\}/g, config.contact?.email || '<EMAIL>')
        .replace(/\{\{RESPONSE_TIME\}\}/g, config.contact?.response_time || '24-48 hours');
}

// 杩愯鏋勫缓
if (require.main === module) {
    build();
}

module.exports = { build };








// ===== hreflang/canonical helpers =====
function trimTrailingSlash(u) { return (u || '').replace(/\/$/, ''); }
function ensureLeadingSlash(p) { return p ? (p.startsWith('/') ? p : '/' + p) : '/'; }
function ensureTrailingSlash(p) { return p.endsWith('/') ? p : p + '/'; }
function makeLangRootUrl(config, lang) {
    const base = trimTrailingSlash(config.seo?.canonical_base_url || config.site_url || 'https://example.com');
    const langPath = (config.i18n?.paths?.[lang] !== undefined)
        ? config.i18n.paths[lang]
        : (lang === 'en' ? '/' : `/${lang}/`);
    const pathNorm = ensureTrailingSlash(ensureLeadingSlash(langPath));
    return base + pathNorm;
}
function buildAlternateLinks(config, pageRelativePath = '') {
    const rel = pageRelativePath ? ensureLeadingSlash(pageRelativePath) : '/';
    const enRoot = makeLangRootUrl(config, 'en');
    const zhRoot = makeLangRootUrl(config, 'zh');
    const enUrl = trimTrailingSlash(enRoot) + (rel === '/' ? '/' : rel);
    const zhUrl = trimTrailingSlash(zhRoot) + (rel === '/' ? '/' : rel);
    const xDefault = enUrl;
    return [
        `<link rel="alternate" hreflang="en" href="${enUrl}" />`,
        `<link rel="alternate" hreflang="zh" href="${zhUrl}" />`,
        `<link rel="alternate" hreflang="x-default" href="${xDefault}" />`
    ].join('\n    ');
}










function buildSwitchUrl(config, pageRelativePath = '') {
    const currentLang = I18N_CTX.lang;
    const targetLang = currentLang === 'zh' ? 'en' : 'zh';
    const root = makeLangRootUrl(config, targetLang);
    const rel = pageRelativePath ? ensureLeadingSlash(pageRelativePath) : '/';
    return trimTrailingSlash(root) + (rel === '/' ? '/' : rel);
}




