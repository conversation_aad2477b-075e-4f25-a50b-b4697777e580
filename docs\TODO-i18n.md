# i18n TODO (en default, zh under /zh/)

<PERSON><PERSON> confirmed:
- Default English at root; Chinese at `/zh/`.
- Phase 1 excludes legal pages i18n.
- Localize only `games.json` meta and feature_blocks (names stay as-is).

---

## URL & SEO
- [ ] Keep mirrored structure: EN at `/`, ZH at `/zh/`.
- [ ] Inject `<html lang>` per language.
- [ ] Add `<link rel="alternate" hreflang="en|zh" ...>` for each page.
- [ ] Set canonical to the current language URL.
- [ ] Generate sitemaps covering both EN and ZH; update robots.txt to point to sitemap index.

## Configuration & Language Packs
- [ ] Add `config.i18n`: `{ default_lang: "en", languages: ["en","zh"], paths: { en: "/", zh: "/zh/" } }`.
- [ ] Create `i18n/en.json` and `i18n/zh.json` with keys:
  - nav/labels: `nav.popular`, `nav.new`
  - buttons: `btn.play_now`, `btn.view_all`, `btn.fullscreen`
  - search: `search.placeholder`
  - footer: `footer.rights`, `footer.tagline`
  - 404: `404.title`, `404.desc`, `404.cta_home`
  - categories: `category.popular.title`, `category.popular.subtitle`, `category.new.title`, `category.new.subtitle`
  - SEO defaults: `seo.default_title`, `seo.default_desc`, `seo.keywords`

## Data Model (backward compatible)
- [ ] Support localized values in `games.json` (keep `name` unchanged):
  - `meta.title` and `meta.description`: string or `{ en, zh }`.
  - `feature_blocks[]` text fields (e.g., `title`, `content`, `text`): string or `{ en, zh }`.
- [ ] Reading rules: prefer current language; fallback to `en`; then empty string if missing.

## Templates
- [ ] Replace hardcoded strings with placeholders:
  - Play, View All, Fullscreen, Search placeholder, Popular/New labels, footer texts, 404 texts.
- [ ] Add `{{LANG}}` into `<html lang="{{LANG}}">`.
- [ ] Inject `{{ALTERNATE_HREFLANG_LINKS}}` container in `<head>`.
- [ ] Add language switcher placeholders: `{{LANG_SWITCH_LABEL}}`, `{{LANG_SWITCH_URL}}` in header.
- [ ] Keep internal links relative to the language root; no cross-language leakage.

Target files:
- `src/templates/_main-layout-template.html`
- `src/templates/game-detail-template.html`
- `src/templates/_list-layout-template.html`
- `src/templates/404.html`
- `src/components/game-card.html` (if any visible label text appears; alt/title may remain data-driven)

## Build Script (build.js)
- [ ] Load `config.i18n` and language packs.
- [ ] Implement `t(key, lang)` with fallback to EN.
- [ ] Implement `localizeGame(game, lang)` to localize `meta` and `feature_blocks` only.
- [ ] Iterate `i18n.languages` and build per language to the correct output root (EN root, ZH `/zh/`).
- [ ] Parameterize link rewriting to respect language base path.
- [ ] Generate per-language `js/games-data.js` with localized URLs and (if needed) labels; keep `name` as-is.
- [ ] Inject `LANG`, `ALTERNATE_HREFLANG_LINKS`, and canonical per page.

## Sitemaps & robots.txt
- [ ] Include both EN and ZH URLs; optionally add xhtml:link for alternates (nice-to-have).
- [ ] Update sitemap index and ensure robots.txt points to sitemap index once.

## QA Checklist
- [ ] English pages render with EN texts; Chinese pages with ZH texts.
- [ ] Language switcher toggles between current page EN↔ZH correctly.
- [ ] Missing translations gracefully fallback to EN (no broken placeholders).
- [ ] All internal links stay within language scope (no unintended cross-lang hops).
- [ ] `<html lang>`, `hreflang` alternates, and canonical tags validated.
- [ ] Games list and detail pages use localized meta and feature_blocks content only; names unchanged.
- [ ] Sitemaps include both languages; 404 page i18n works (legal pages excluded in phase 1).

## Out of Scope (Phase 1)
- Legal pages i18n; will be handled in a later phase.

## Deliverables
- Language packs (`i18n/en.json`, `i18n/zh.json`).
- Updated templates and `build.js` with i18n pipeline.
- Updated `config.json` (`i18n` section) and backward-compatible content loaders.
- Localized builds at `dist/` (EN) and `dist/zh/` (ZH), with sitemaps and robots.txt.

